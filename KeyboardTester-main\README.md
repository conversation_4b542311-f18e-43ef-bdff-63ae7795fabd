## Online Keyboard Tester ⌨️

<p align="center">
  <img src="https://raw.githubusercontent.com/Mostafa-Abbasi/KeyboardTester/main/img/demo.gif" alt="App Demo GIF">
</p>

<br/><br/>

## Overview 🌐

Welcome to the Online Keyboard Tester! This interactive web application is designed to help you ensure that every key on your keyboard is functioning perfectly. Whether you're a programmer, gamer, or just a keyboard enthusiast, it's essential to have confidence in your keyboard's reliability.
<br/><br/>

## How to Use 🚀

Just head over to **[Online Keyboard Tester Website](https://mostafa-abbasi.github.io/KeyboardTester)** and start pressing those keys! It will instantly respond to your every keystroke.
<br/><br/>

## Features 🌟

- **🔦 Comprehensive Testing:** Press every key and watch them light up! Make sure your keyboard is in perfect working order.

- **🖌️ Themes:** Customize your testing experience with different themes. From "Retro" vibes to a cool "Navy Blue" look, find the one that suits you best.

- **⚙️ Layout Options:** Toggle between `Full-size` and `Tenkeyless` layouts for a personalized testing session.
  <br/><br/>

## Who is this Tool For? 🎯

This tool is your go-to, whether you're part of:
<br/><br/>

- **New Keyboard Owners**: Test your fresh or pre-loved keyboards and laptops for reliable functionality
  <br/><br/>
- **Keyboard Geeks**: Ensure your personalized mech-keyboards rock after all those fancy modifications
  <br/><br/>
- **Fellow Gamers**: Put your gaming keyboard to the test with speed and simultaneous key pressing checks
  <br/><br/>
- **Programmers**: Guarantee those keystrokes are flawless for your smooth coding adventures all-day long
  <br/><br/>
- **Programmers (Again!)**: Dive into the codebase and get inspired on that sleek standard keyboard layout
  <br/><br/>
- **Standard Windows Layout Users**: If you roll with the standard Windows layout, it's fine-tuned for you!
  <br/><br/>

## Getting Started 🧑‍💻

If you want to run this project locally or contribute to its development, follow these simple steps:

**1.** Clone the repository to your local machine:

```bash
  git clone https://github.com/mostafa-abbasi/KeyboardTester.git
```

**2.** Open the index.html file in your web browser.

**3.** Start pressing those keys and have some fun! 🎉
<br/><br/>

## Limitations ⚠️

❗ Please be aware of the following limitations: ❗

- This tool **cannot** recognize the **`fn`** keypress on the keyboard due to it being a hardware key and not being registered by browser key events. As a result, pressing the **`fn`** key may not produce any visual feedback within the application.
- While this tool is accessible on mobile browsers, please be aware that it is primarily optimized for desktop use. If possible, we **highly recommend** utilizing a tablet or switching your mobile orientation to horizontal mode for an enhanced experience. For optimal performance, consider using a desktop if available.
- Special keys like media keys, macro keys, or other non-standard keys found on certain keyboards or laptops **are not** compatible with this tool.
- Layouts other than QWERTY **may not** display accurately.
  <br/><br/>

## FAQs and Troubleshooting 🛠️

If you encounter any issues while using the Online Keyboard Tester, check out these frequently asked questions and troubleshooting tips:
<br/><br/>

**Q: Will this tool handle touchpad keys, macros, media keys and etc.?**

- A: Nope, this tool is more into the basic keyboard stuff. It will not recognize touchpad, macro, media, etc.

**Q: Can I use this tool on mobile or tablet devices perfectly fine?**

- A: Tuned for desktop use, but can work on mobile/tablet with an external keyboard and lackluster visuals

**Q: No visual feedback after 'fn' key press. What to do now?**

- A: 'fn' key is a hardware key and is not registered by browser key events. It can't be tested using this tool.

**Q: Certain keys doesn't register. Any recommendations?**

- A: It might be a hardware issue. Clean affected keys or refer to your keyboard's manual for troubleshooting.

**Q: Works for non-standard layouts, like Apple Mac?**

- A: Primarily designed for standard QWERTY Windows/Linux layouts. May work with others, But not optimized.

**Q: Recommended browsers for this web app?**

- A: Tested on Firefox, Edge and chrome. For the slickest experience, latest Chrome version is recommended.
  <br/><br/>

## Project Origins 🌱

This project began as a creative exploration of web development and a playful fascination with keyboards. Over time, it has evolved into a handy tool for anyone seeking to validate their keyboard's functionality. In addition to its core feature, the Online Keyboard Tester now offers additional options like themes and layouts to enhance the testing experience.
<br/><br/>

## Contributing 🤝

If you're interested in contributing to this project, feel free to fork the repository and submit your own improvements. Whether it's adding new themes, enhancing keyboard layout options, or improving the code, your contributions are always welcome!

You can contact <NAME_EMAIL> 📧
<br/><br/>

## License 📝

This project is open-source and available under the [MIT License](https://opensource.org/license/mit/). 📜
<br/><br/>

Enjoy testing your keyboard! 😊

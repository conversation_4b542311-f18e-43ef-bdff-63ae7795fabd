﻿namespace diagnosticsTool
{
    partial class Form1
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            mainPanel = new Panel();
            systemInfoButton = new Button();
            webcamTesterButton = new Button();
            keyboardTesterButton = new Button();
            deviceManagerButton = new Button();
            titleLabel = new Label();
            resultsPanel = new Panel();
            pictureBox3 = new PictureBox();
            pictureBox2 = new PictureBox();
            statusLabel = new Label();
            webCamBox = new PictureBox();
            resultsLabel = new Label();
            mainPanel.SuspendLayout();
            resultsPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox3).BeginInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox2).BeginInit();
            ((System.ComponentModel.ISupportInitialize)webCamBox).BeginInit();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.BackColor = Color.FromArgb(240, 248, 255);
            mainPanel.BorderStyle = BorderStyle.FixedSingle;
            mainPanel.Controls.Add(systemInfoButton);
            mainPanel.Controls.Add(webcamTesterButton);
            mainPanel.Controls.Add(keyboardTesterButton);
            mainPanel.Controls.Add(deviceManagerButton);
            mainPanel.Controls.Add(titleLabel);
            mainPanel.Location = new Point(20, 20);
            mainPanel.Name = "mainPanel";
            mainPanel.Size = new Size(300, 520);
            mainPanel.TabIndex = 0;
            // 
            // systemInfoButton
            // 
            systemInfoButton.BackColor = Color.FromArgb(138, 43, 226);
            systemInfoButton.FlatAppearance.BorderSize = 0;
            systemInfoButton.FlatStyle = FlatStyle.Flat;
            systemInfoButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point);
            systemInfoButton.ForeColor = Color.White;
            systemInfoButton.Location = new Point(20, 320);
            systemInfoButton.Name = "systemInfoButton";
            systemInfoButton.Size = new Size(260, 60);
            systemInfoButton.TabIndex = 4;
            systemInfoButton.Text = "💻 System Information";
            systemInfoButton.UseVisualStyleBackColor = false;
            systemInfoButton.Click += SystemInfoButton_Click;
            // 
            // webcamTesterButton
            // 
            webcamTesterButton.BackColor = Color.FromArgb(255, 140, 0);
            webcamTesterButton.FlatAppearance.BorderSize = 0;
            webcamTesterButton.FlatStyle = FlatStyle.Flat;
            webcamTesterButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point);
            webcamTesterButton.ForeColor = Color.White;
            webcamTesterButton.Location = new Point(20, 240);
            webcamTesterButton.Name = "webcamTesterButton";
            webcamTesterButton.Size = new Size(260, 60);
            webcamTesterButton.TabIndex = 3;
            webcamTesterButton.Text = "📷 Webcam Tester";
            webcamTesterButton.UseVisualStyleBackColor = false;
            webcamTesterButton.Click += WebcamTesterButton_Click;
            // 
            // keyboardTesterButton
            // 
            keyboardTesterButton.BackColor = Color.FromArgb(34, 139, 34);
            keyboardTesterButton.FlatAppearance.BorderSize = 0;
            keyboardTesterButton.FlatStyle = FlatStyle.Flat;
            keyboardTesterButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point);
            keyboardTesterButton.ForeColor = Color.White;
            keyboardTesterButton.Location = new Point(20, 160);
            keyboardTesterButton.Name = "keyboardTesterButton";
            keyboardTesterButton.Size = new Size(260, 60);
            keyboardTesterButton.TabIndex = 2;
            keyboardTesterButton.Text = "🎹 Keyboard Tester";
            keyboardTesterButton.UseVisualStyleBackColor = false;
            keyboardTesterButton.Click += KeyboardTesterButton_Click;
            // 
            // deviceManagerButton
            // 
            deviceManagerButton.BackColor = Color.FromArgb(70, 130, 180);
            deviceManagerButton.FlatAppearance.BorderSize = 0;
            deviceManagerButton.FlatStyle = FlatStyle.Flat;
            deviceManagerButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point);
            deviceManagerButton.ForeColor = Color.White;
            deviceManagerButton.Image = Properties.Resources.Device_Manager_Icon;
            deviceManagerButton.ImageAlign = ContentAlignment.MiddleLeft;
            deviceManagerButton.Location = new Point(20, 80);
            deviceManagerButton.Name = "deviceManagerButton";
            deviceManagerButton.Padding = new Padding(10, 0, 0, 0);
            deviceManagerButton.Size = new Size(260, 60);
            deviceManagerButton.TabIndex = 1;
            deviceManagerButton.Text = "   Device Manager";
            deviceManagerButton.UseVisualStyleBackColor = false;
            deviceManagerButton.Click += DeviceManagerButton_Click;
            // 
            // titleLabel
            // 
            titleLabel.AutoSize = true;
            titleLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold, GraphicsUnit.Point);
            titleLabel.ForeColor = Color.FromArgb(25, 25, 112);
            titleLabel.Location = new Point(20, 20);
            titleLabel.Name = "titleLabel";
            titleLabel.Size = new Size(244, 32);
            titleLabel.TabIndex = 0;
            titleLabel.Text = "🔧 Diagnostics Tool";
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // resultsPanel
            // 
            resultsPanel.BackColor = Color.White;
            resultsPanel.BorderStyle = BorderStyle.FixedSingle;
            resultsPanel.Controls.Add(pictureBox3);
            resultsPanel.Controls.Add(pictureBox2);
            resultsPanel.Controls.Add(statusLabel);
            resultsPanel.Controls.Add(webCamBox);
            resultsPanel.Controls.Add(resultsLabel);
            resultsPanel.Location = new Point(340, 20);
            resultsPanel.Name = "resultsPanel";
            resultsPanel.Size = new Size(720, 520);
            resultsPanel.TabIndex = 1;
            // 
            // pictureBox3
            // 
            pictureBox3.Image = Properties.Resources.Screenshot_2023_10_23_145840;
            pictureBox3.Location = new Point(618, 470);
            pictureBox3.Name = "pictureBox3";
            pictureBox3.Size = new Size(97, 45);
            pictureBox3.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBox3.TabIndex = 5;
            pictureBox3.TabStop = false;
            pictureBox3.Click += pictureBox3_Click;
            // 
            // pictureBox2
            // 
            pictureBox2.Image = Properties.Resources.Screenshot_2023_10_23_144935;
            pictureBox2.Location = new Point(475, 470);
            pictureBox2.Name = "pictureBox2";
            pictureBox2.Size = new Size(137, 45);
            pictureBox2.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBox2.TabIndex = 4;
            pictureBox2.TabStop = false;
            // 
            // statusLabel
            // 
            statusLabel.AutoSize = true;
            statusLabel.Font = new Font("Segoe UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            statusLabel.ForeColor = Color.FromArgb(70, 70, 70);
            statusLabel.Location = new Point(20, 470);
            statusLabel.Name = "statusLabel";
            statusLabel.Size = new Size(188, 19);
            statusLabel.TabIndex = 2;
            statusLabel.Text = "Ready - Select a test to begin";
            // 
            // webCamBox
            // 
            webCamBox.BackColor = Color.FromArgb(245, 245, 245);
            webCamBox.BorderStyle = BorderStyle.FixedSingle;
            webCamBox.Location = new Point(20, 70);
            webCamBox.Name = "webCamBox";
            webCamBox.Size = new Size(320, 240);
            webCamBox.SizeMode = PictureBoxSizeMode.Zoom;
            webCamBox.TabIndex = 1;
            webCamBox.TabStop = false;
            webCamBox.Click += webCamBox_Click;
            // 
            // resultsLabel
            // 
            resultsLabel.AutoSize = true;
            resultsLabel.Font = new Font("Segoe UI", 16F, FontStyle.Bold, GraphicsUnit.Point);
            resultsLabel.ForeColor = Color.FromArgb(25, 25, 112);
            resultsLabel.Location = new Point(20, 20);
            resultsLabel.Name = "resultsLabel";
            resultsLabel.Size = new Size(171, 30);
            resultsLabel.TabIndex = 0;
            resultsLabel.Text = "📊 Test Results";
            // 
            // Form1
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(248, 248, 255);
            ClientSize = new Size(1080, 560);
            Controls.Add(resultsPanel);
            Controls.Add(mainPanel);
            FormBorderStyle = FormBorderStyle.FixedSingle;
            MaximizeBox = false;
            Name = "Form1";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "Professional Diagnostics Tool v1.0";
            mainPanel.ResumeLayout(false);
            mainPanel.PerformLayout();
            resultsPanel.ResumeLayout(false);
            resultsPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox3).EndInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox2).EndInit();
            ((System.ComponentModel.ISupportInitialize)webCamBox).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private Panel mainPanel;
        private Label titleLabel;
        private Button deviceManagerButton;
        private Button keyboardTesterButton;
        private Button webcamTesterButton;
        private Button systemInfoButton;
        private Panel resultsPanel;
        private Label resultsLabel;
        private PictureBox webCamBox;
        private Label statusLabel;
        private PictureBox pictureBox2;
        private PictureBox pictureBox3;
    }
}
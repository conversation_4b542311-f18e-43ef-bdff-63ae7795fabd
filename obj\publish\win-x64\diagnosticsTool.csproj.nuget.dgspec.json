{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\diagnosticsTool\\diagnosticsTool.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\diagnosticsTool\\diagnosticsTool.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\diagnosticsTool\\diagnosticsTool.csproj", "projectName": "diagnosticsTool", "projectPath": "C:\\Users\\<USER>\\source\\repos\\diagnosticsTool\\diagnosticsTool.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\diagnosticsTool\\obj\\publish\\win-x64\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"AForge.Video": {"target": "Package", "version": "[2.2.5, )"}, "AForge.Video.DirectShow": {"target": "Package", "version": "[2.2.5, )"}, "Microsoft.NET.ILLink.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "System.Management": {"target": "Package", "version": "[7.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}
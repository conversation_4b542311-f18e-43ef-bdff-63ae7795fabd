using System.Diagnostics;
using System;
using System.Linq;
using System.Management;
using AForge.Video;
using AForge.Video.DirectShow;
using System.Windows.Forms;
using System.Diagnostics.Tracing;
using System.Drawing;

namespace diagnosticsTool
{
    public partial class Form1 : Form
    {
        private FilterInfoCollection videoDevices;
        private VideoCaptureDevice videoSource;
        private bool isCapturing = false;

        public Form1()
        {
            InitializeComponent();
            UpdateStatus("Application started - Ready for diagnostics");
        }

        private void DeviceManagerButton_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("Opening Device Manager...");
                Process.Start("mmc.exe", "devmgmt.msc");
                UpdateStatus("Device Manager opened successfully");
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error opening Device Manager: {ex.Message}");
                MessageBox.Show($"Failed to open Device Manager: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void WebcamTesterButton_Click(object sender, EventArgs e)
        {
            UpdateStatus("Starting webcam test...");
            webCamBox_Click(sender, e);
        }

        private void SystemInfoButton_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("Opening System Information...");
                Process.Start("msinfo32.exe");
                UpdateStatus("System Information opened successfully");
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error opening System Information: {ex.Message}");
                MessageBox.Show($"Failed to open System Information: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateStatus(string message)
        {
            if (statusLabel != null)
            {
                statusLabel.Text = $"{DateTime.Now:HH:mm:ss} - {message}";
                statusLabel.Refresh();
            }
        }

       

        private void webCamBox_Click(object sender, EventArgs e)
        {
            try
            {
                if (isCapturing)
                {
                    UpdateStatus("Stopping webcam...");
                    videoSource.SignalToStop();
                    videoSource.WaitForStop();

                    // Release the video source
                    videoSource = null;

                    isCapturing = false;
                    webCamBox.Image = null; // Clear the PictureBox
                    webCamBox.BackColor = Color.FromArgb(245, 245, 245);
                    UpdateStatus("Webcam stopped - Click to start again");
                }
                else
                {
                    UpdateStatus("Searching for webcam devices...");
                    videoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);

                    if (videoDevices.Count == 0)
                    {
                        UpdateStatus("No webcam devices found");
                        MessageBox.Show("No video devices found. Please check if a webcam is connected.",
                            "No Webcam Found", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    UpdateStatus($"Found {videoDevices.Count} webcam device(s) - Starting capture...");
                    videoSource = new VideoCaptureDevice(videoDevices[0].MonikerString);
                    videoSource.NewFrame += new NewFrameEventHandler(video_NewFrame);
                    videoSource.Start();

                    isCapturing = true;
                    UpdateStatus("Webcam started successfully - Click to stop");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"Webcam error: {ex.Message}");
                MessageBox.Show($"Webcam error: {ex.Message}", "Webcam Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void video_NewFrame(object sender, NewFrameEventArgs eventArgs)
        {
            // This event is triggered each time a new frame is captured.
            // You can process the frame here.
            webCamBox.Image = (Bitmap)eventArgs.Frame.Clone();
        }

        private void KeyboardTesterButton_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("Opening Keyboard Tester...");
                KeyboardTesterForm keyboardTester = new KeyboardTesterForm();
                keyboardTester.Show();
                UpdateStatus("Keyboard Tester opened successfully");
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error opening Keyboard Tester: {ex.Message}");
                MessageBox.Show($"Failed to open Keyboard Tester: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // Clean up webcam resources
            if (isCapturing && videoSource != null)
            {
                try
                {
                    videoSource.SignalToStop();
                    videoSource.WaitForStop();
                }
                catch { }
            }
            base.OnFormClosing(e);
        }

        private void pictureBox1_Click(object sender, EventArgs e)
        {

        }
    }
}
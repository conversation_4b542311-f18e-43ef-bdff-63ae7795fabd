using System.Diagnostics;
using System;
using System.Linq;
using System.Management;
using AForge.Video;
using AForge.Video.DirectShow;
using System.Windows.Forms;
using System.Diagnostics.Tracing;
using System.Drawing;

namespace diagnosticsTool
{
    public partial class Form1 : Form
    {
        private FilterInfoCollection videoDevices;
        private VideoCaptureDevice videoSource;
        private bool isCapturing = false;
        private ToolTip activationToolTip;

        public Form1()
        {
            InitializeComponent();
            UpdateStatus("Application started - Ready for diagnostics");

            // Set up tooltip for activation status picture
            activationToolTip = new ToolTip();
            activationToolTip.SetToolTip(pictureBox3, "Windows Activation Status - Click to refresh");

            // Automatically check Windows activation status on startup
            CheckWindowsActivationStatus();
        }

        private void DeviceManagerButton_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("Opening Device Manager...");
                Process.Start("mmc.exe", "devmgmt.msc");
                UpdateStatus("Device Manager opened successfully");
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error opening Device Manager: {ex.Message}");
                MessageBox.Show($"Failed to open Device Manager: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void WebcamTesterButton_Click(object sender, EventArgs e)
        {
            UpdateStatus("Starting webcam test...");
            webCamBox_Click(sender, e);
        }

        private void SystemInfoButton_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("Opening System Information...");
                Process.Start("msinfo32.exe");
                UpdateStatus("System Information opened successfully");
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error opening System Information: {ex.Message}");
                MessageBox.Show($"Failed to open System Information: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateStatus(string message)
        {
            if (statusLabel != null)
            {
                statusLabel.Text = $"{DateTime.Now:HH:mm:ss} - {message}";
                statusLabel.Refresh();
            }
        }

        private void pictureBox3_Click(object sender, EventArgs e)
        {
            // Manual check when user clicks the image
            UpdateStatus("Checking Windows activation status...");
            CheckWindowsActivationStatus();
        }

        private void CheckWindowsActivationStatus()
        {
            try
            {
                bool isActivated = IsWindowsActivated();

                if (isActivated)
                {
                    // Windows is activated - show activated image
                    pictureBox3.Image = Properties.Resources.Screenshot_2023_10_23_144947;
                    UpdateStatus("Windows activation status: ACTIVATED ✓");

                    // Update tooltip for activated status
                    if (activationToolTip != null)
                    {
                        activationToolTip.SetToolTip(pictureBox3, "Windows is ACTIVATED ✓ - Click to refresh");
                    }
                }
                else
                {
                    // Windows is not activated - show not activated image
                    pictureBox3.Image = Properties.Resources.Screenshot_2023_10_23_145840;
                    UpdateStatus("Windows activation status: NOT ACTIVATED ⚠️");

                    // Update tooltip for not activated status
                    if (activationToolTip != null)
                    {
                        activationToolTip.SetToolTip(pictureBox3, "Windows is NOT ACTIVATED ⚠️ - Click to refresh");
                    }
                }
            }
            catch (Exception ex)
            {
                // If there's an error checking activation, show the not activated image as default
                pictureBox3.Image = Properties.Resources.Screenshot_2023_10_23_145840;
                UpdateStatus($"Error checking Windows activation: {ex.Message}");

                // Update tooltip for error status
                if (activationToolTip != null)
                {
                    activationToolTip.SetToolTip(pictureBox3, "Error checking activation status - Click to retry");
                }
            }
        }

        private bool IsWindowsActivated()
        {
            try
            {
                // Method 1: Check using WMI for activated Windows licenses
                using (var searcher = new ManagementObjectSearcher("SELECT LicenseStatus FROM SoftwareLicensingProduct WHERE ApplicationID = '55c92734-d682-4d71-983e-d6ec3f16059f' AND PartialProductKey IS NOT NULL"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var licenseStatus = obj["LicenseStatus"];
                        if (licenseStatus != null)
                        {
                            int status = Convert.ToInt32(licenseStatus);
                            // LicenseStatus = 1 means Licensed (Activated)
                            if (status == 1)
                            {
                                return true;
                            }
                        }
                    }
                }

                // Method 2: Alternative check using slmgr command (if WMI fails)
                try
                {
                    var processInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = "cscript",
                        Arguments = "//nologo C:\\Windows\\System32\\slmgr.vbs /xpr",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        CreateNoWindow = true
                    };

                    using (var process = System.Diagnostics.Process.Start(processInfo))
                    {
                        if (process != null)
                        {
                            string output = process.StandardOutput.ReadToEnd();
                            process.WaitForExit();

                            // Check if output contains activation confirmation
                            if (output.ToLower().Contains("permanently activated") ||
                                output.ToLower().Contains("licensed") ||
                                output.ToLower().Contains("activated"))
                            {
                                return true;
                            }
                        }
                    }
                }
                catch
                {
                    // If slmgr fails, continue to return false
                }

                return false; // No activation found
            }
            catch (Exception ex)
            {
                // Log the error but don't show it to user during automatic check
                System.Diagnostics.Debug.WriteLine($"Windows activation check failed: {ex.Message}");
                return false;
            }
        }

        private void webCamBox_Click(object sender, EventArgs e)
        {
            try
            {
                if (isCapturing)
                {
                    UpdateStatus("Stopping webcam...");
                    videoSource.SignalToStop();
                    videoSource.WaitForStop();

                    // Release the video source
                    videoSource = null;

                    isCapturing = false;
                    webCamBox.Image = null; // Clear the PictureBox
                    webCamBox.BackColor = Color.FromArgb(245, 245, 245);
                    UpdateStatus("Webcam stopped - Click to start again");
                }
                else
                {
                    UpdateStatus("Searching for webcam devices...");
                    videoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);

                    if (videoDevices.Count == 0)
                    {
                        UpdateStatus("No webcam devices found");
                        MessageBox.Show("No video devices found. Please check if a webcam is connected.",
                            "No Webcam Found", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    UpdateStatus($"Found {videoDevices.Count} webcam device(s) - Starting capture...");
                    videoSource = new VideoCaptureDevice(videoDevices[0].MonikerString);
                    videoSource.NewFrame += new NewFrameEventHandler(video_NewFrame);
                    videoSource.Start();

                    isCapturing = true;
                    UpdateStatus("Webcam started successfully - Click to stop");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"Webcam error: {ex.Message}");
                MessageBox.Show($"Webcam error: {ex.Message}", "Webcam Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void video_NewFrame(object sender, NewFrameEventArgs eventArgs)
        {
            // This event is triggered each time a new frame is captured.
            // You can process the frame here.
            webCamBox.Image = (Bitmap)eventArgs.Frame.Clone();
        }

        private void KeyboardTesterButton_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("Opening Keyboard Tester...");
                KeyboardTesterForm keyboardTester = new KeyboardTesterForm();
                keyboardTester.Show();
                UpdateStatus("Keyboard Tester opened successfully");
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error opening Keyboard Tester: {ex.Message}");
                MessageBox.Show($"Failed to open Keyboard Tester: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // Clean up webcam resources
            if (isCapturing && videoSource != null)
            {
                try
                {
                    videoSource.SignalToStop();
                    videoSource.WaitForStop();
                }
                catch { }
            }

            // Clean up tooltip
            if (activationToolTip != null)
            {
                activationToolTip.Dispose();
            }

            base.OnFormClosing(e);
        }

        private void pictureBox1_Click(object sender, EventArgs e)
        {

        }
    }
}
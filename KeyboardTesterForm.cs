using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System.Runtime.InteropServices;

namespace diagnosticsTool
{
    public partial class KeyboardTesterForm : Form
    {
        // Win32 API import for detecting specific left/right modifier keys and low-level hooks
        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        [DllImport("user32.dll")]
        private static extern IntPtr SetWindowsHookEx(int idHook, LowLevelKeyboardProc lpfn, IntPtr hMod, uint dwThreadId);

        [DllImport("user32.dll")]
        private static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll")]
        private static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode, IntPtr wParam, IntPtr lParam);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetModuleHandle(string? lpModuleName);

        private const int WH_KEYBOARD_LL = 13;
        private const int WM_KEYDOWN_HOOK = 0x0100;
        private const int WM_SYSKEYDOWN_HOOK = 0x0104;
        private const int WM_KEYUP_HOOK = 0x0101;
        private const int WM_SYSKEYUP_HOOK = 0x0105;

        private delegate IntPtr LowLevelKeyboardProc(int nCode, IntPtr wParam, IntPtr lParam);
        private LowLevelKeyboardProc _proc;
        private static IntPtr _hookID = IntPtr.Zero;
        private static KeyboardTesterForm? _instance;
        private static bool _testingModeActive = false;

        private Dictionary<Keys, Button> keyButtons = new Dictionary<Keys, Button>();
        private HashSet<Keys> testedKeys = new HashSet<Keys>();
        private Label statusLabel;
        private Label testingModeLabel;
        private Button resetButton;
        private Button toggleTestingModeButton;
        private Panel mainKeyboardPanel;
        private Panel functionKeysPanel;
        private Panel numpadPanel;

        // Timer for polling special keys that are hard to detect
        private System.Windows.Forms.Timer keyPollingTimer;
        private HashSet<Keys> lastPolledKeys = new HashSet<Keys>();

        public KeyboardTesterForm()
        {
            InitializeComponent();
            InitializeKeyboard();
            this.KeyPreview = true;
            this.KeyDown += KeyboardTesterForm_KeyDown;
            this.KeyUp += KeyboardTesterForm_KeyUp;

            // Set up low-level keyboard hook for comprehensive key interception
            _instance = this;
            _proc = HookCallback;
            _hookID = SetHook(_proc);

            // Start in testing mode by default
            _testingModeActive = true;
            UpdateStatusLabel();
            UpdateTestingModeDisplay();

            // Set up polling timer for special keys (Windows key, Menu key)
            keyPollingTimer = new System.Windows.Forms.Timer();
            keyPollingTimer.Interval = 50; // Poll every 50ms
            keyPollingTimer.Tick += KeyPollingTimer_Tick;
            keyPollingTimer.Start();
        }

        public override bool PreProcessMessage(ref Message msg)
        {
            // Additional handling for Windows key and Menu key that might be missed by other methods
            if (_testingModeActive && (msg.Msg == 0x0100 || msg.Msg == 0x0104)) // WM_KEYDOWN or WM_SYSKEYDOWN
            {
                int vkCode = (int)msg.WParam;
                Keys? keyToTest = null;

                switch (vkCode)
                {
                    case 0x5B: // VK_LWIN
                        keyToTest = Keys.LWin;
                        break;
                    case 0x5C: // VK_RWIN
                        keyToTest = Keys.RWin;
                        break;
                    case 0x5D: // VK_APPS
                        keyToTest = Keys.Apps;
                        break;
                }

                if (keyToTest.HasValue && keyButtons.ContainsKey(keyToTest.Value))
                {
                    testedKeys.Add(keyToTest.Value);
                    keyButtons[keyToTest.Value].BackColor = Color.Yellow;
                    UpdateStatusLabel();

                    var timer = new System.Windows.Forms.Timer();
                    timer.Interval = 200;
                    timer.Tick += (s, args) =>
                    {
                        keyButtons[keyToTest.Value].BackColor = Color.LightGreen;
                        timer.Stop();
                        timer.Dispose();
                    };
                    timer.Start();

                    return true; // Suppress further processing
                }
            }

            return base.PreProcessMessage(ref msg);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 500);
            this.Text = "Professional Keyboard Tester v1.0";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(248, 248, 255);
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Icon = SystemIcons.Application;
            
            // Status label
            statusLabel = new Label
            {
                Text = "Press any key to test it. Green = tested, Gray = untested, Yellow = currently pressed",
                Location = new Point(20, 20),
                Size = new Size(600, 20),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 25, 112)
            };
            this.Controls.Add(statusLabel);

            // Testing mode label
            testingModeLabel = new Label
            {
                Text = "🔒 TESTING MODE: System keys disabled",
                Location = new Point(20, 45),
                Size = new Size(400, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(220, 20, 60),
                BackColor = Color.FromArgb(255, 255, 224)
            };
            this.Controls.Add(testingModeLabel);

            // Reset button
            resetButton = new Button
            {
                Text = "🔄 Reset All Keys",
                Location = new Point(650, 15),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9, FontStyle.Bold)
            };
            resetButton.FlatAppearance.BorderSize = 0;
            resetButton.Click += ResetButton_Click;
            this.Controls.Add(resetButton);

            // Toggle testing mode button
            toggleTestingModeButton = new Button
            {
                Text = "Disable Testing Mode",
                Location = new Point(780, 15),
                Size = new Size(150, 30),
                BackColor = Color.FromArgb(220, 20, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9, FontStyle.Bold)
            };
            toggleTestingModeButton.FlatAppearance.BorderSize = 0;
            toggleTestingModeButton.Click += ToggleTestingModeButton_Click;
            this.Controls.Add(toggleTestingModeButton);
            
            // Function keys panel
            functionKeysPanel = new Panel
            {
                Location = new Point(20, 75),
                Size = new Size(800, 50),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(240, 248, 255)
            };
            this.Controls.Add(functionKeysPanel);

            // Main keyboard panel
            mainKeyboardPanel = new Panel
            {
                Location = new Point(20, 135),
                Size = new Size(800, 320),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White
            };
            this.Controls.Add(mainKeyboardPanel);

            // Numeric keypad panel
            numpadPanel = new Panel
            {
                Location = new Point(840, 135),
                Size = new Size(200, 320),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(245, 245, 245)
            };
            this.Controls.Add(numpadPanel);
            
            this.ResumeLayout(false);
        }

        private void InitializeKeyboard()
        {
            CreateFunctionKeys();
            CreateMainKeyboard();
            CreateNumericKeypad();
        }

        private void CreateFunctionKeys()
        {
            // Function keys F1-F12
            var functionKeys = new List<Keys>
            {
                Keys.F1, Keys.F2, Keys.F3, Keys.F4, Keys.F5, Keys.F6,
                Keys.F7, Keys.F8, Keys.F9, Keys.F10, Keys.F11, Keys.F12
            };

            int x = 10;
            foreach (var key in functionKeys)
            {
                Button btn = CreateKeyButton(key.ToString(), new Size(60, 30), new Point(x, 5));
                keyButtons[key] = btn;
                functionKeysPanel.Controls.Add(btn);
                x += 65;
            }
        }

        private void CreateMainKeyboard()
        {
            // Row 1: Numbers and symbols
            var row1Keys = new List<(string text, Keys key)>
            {
                ("`", Keys.Oemtilde), ("1", Keys.D1), ("2", Keys.D2), ("3", Keys.D3), ("4", Keys.D4),
                ("5", Keys.D5), ("6", Keys.D6), ("7", Keys.D7), ("8", Keys.D8), ("9", Keys.D9),
                ("0", Keys.D0), ("-", Keys.OemMinus), ("=", Keys.Oemplus), ("Backspace", Keys.Back)
            };

            // Row 2: QWERTY
            var row2Keys = new List<(string text, Keys key)>
            {
                ("Tab", Keys.Tab), ("Q", Keys.Q), ("W", Keys.W), ("E", Keys.E), ("R", Keys.R),
                ("T", Keys.T), ("Y", Keys.Y), ("U", Keys.U), ("I", Keys.I), ("O", Keys.O),
                ("P", Keys.P), ("[", Keys.OemOpenBrackets), ("]", Keys.OemCloseBrackets), ("\\", Keys.OemBackslash)
            };

            // Row 3: ASDF
            var row3Keys = new List<(string text, Keys key)>
            {
                ("Caps", Keys.CapsLock), ("A", Keys.A), ("S", Keys.S), ("D", Keys.D), ("F", Keys.F),
                ("G", Keys.G), ("H", Keys.H), ("J", Keys.J), ("K", Keys.K), ("L", Keys.L),
                (";", Keys.OemSemicolon), ("'", Keys.OemQuotes), ("Enter", Keys.Enter)
            };

            // Row 4: ZXCV
            var row4Keys = new List<(string text, Keys key)>
            {
                ("Shift", Keys.LShiftKey), ("Z", Keys.Z), ("X", Keys.X), ("C", Keys.C), ("V", Keys.V),
                ("B", Keys.B), ("N", Keys.N), ("M", Keys.M), (",", Keys.Oemcomma), (".", Keys.OemPeriod),
                ("/", Keys.OemQuestion), ("Shift", Keys.RShiftKey)
            };

            // Row 5: Bottom row
            var row5Keys = new List<(string text, Keys key)>
            {
                ("Ctrl", Keys.LControlKey), ("Win", Keys.LWin), ("Alt", Keys.LMenu), ("Space", Keys.Space),
                ("Alt", Keys.RMenu), ("Fn", Keys.None), ("Menu", Keys.Apps), ("Ctrl", Keys.RControlKey)
            };

            var allRows = new List<List<(string text, Keys key)>> { row1Keys, row2Keys, row3Keys, row4Keys, row5Keys };

            int y = 10;
            foreach (var row in allRows)
            {
                int x = 10;
                foreach (var (text, key) in row)
                {
                    Size buttonSize = GetKeySize(text);
                    Button btn = CreateKeyButton(text, buttonSize, new Point(x, y));
                    keyButtons[key] = btn;
                    mainKeyboardPanel.Controls.Add(btn);
                    x += buttonSize.Width + 2;
                }
                y += 45;
            }

            // Navigation keys cluster (moved further right to avoid overlap with "\" key)
            var navKeys = new List<(string text, Keys key, Point location, Size size)>
            {
                // Top row of navigation keys
                ("Ins", Keys.Insert, new Point(650, 50), new Size(35, 25)),
                ("Home", Keys.Home, new Point(690, 50), new Size(35, 25)),
                ("PgUp", Keys.PageUp, new Point(730, 50), new Size(35, 25)),

                // Bottom row of navigation keys
                ("Del", Keys.Delete, new Point(650, 80), new Size(35, 25)),
                ("End", Keys.End, new Point(690, 80), new Size(35, 25)),
                ("PgDn", Keys.PageDown, new Point(730, 80), new Size(35, 25))
            };

            foreach (var (text, key, location, size) in navKeys)
            {
                Button btn = CreateKeyButton(text, size, location);
                keyButtons[key] = btn;
                mainKeyboardPanel.Controls.Add(btn);
            }

            // Arrow keys (properly positioned below navigation keys)
            var arrowKeys = new List<(string text, Keys key, Point location)>
            {
                ("↑", Keys.Up, new Point(690, 120)),
                ("←", Keys.Left, new Point(665, 145)),
                ("↓", Keys.Down, new Point(690, 145)),
                ("→", Keys.Right, new Point(715, 145))
            };

            foreach (var (text, key, location) in arrowKeys)
            {
                Button btn = CreateKeyButton(text, new Size(30, 25), location);
                keyButtons[key] = btn;
                mainKeyboardPanel.Controls.Add(btn);
            }
        }

        private void CreateNumericKeypad()
        {
            // Numeric keypad layout
            var numpadKeys = new List<(string text, Keys key, Point location, Size size)>
            {
                ("Num", Keys.NumLock, new Point(10, 10), new Size(40, 30)),
                ("/", Keys.Divide, new Point(55, 10), new Size(40, 30)),
                ("*", Keys.Multiply, new Point(100, 10), new Size(40, 30)),
                ("-", Keys.Subtract, new Point(145, 10), new Size(40, 30)),
                
                ("7", Keys.NumPad7, new Point(10, 45), new Size(40, 30)),
                ("8", Keys.NumPad8, new Point(55, 45), new Size(40, 30)),
                ("9", Keys.NumPad9, new Point(100, 45), new Size(40, 30)),
                ("+", Keys.Add, new Point(145, 45), new Size(40, 65)),
                
                ("4", Keys.NumPad4, new Point(10, 80), new Size(40, 30)),
                ("5", Keys.NumPad5, new Point(55, 80), new Size(40, 30)),
                ("6", Keys.NumPad6, new Point(100, 80), new Size(40, 30)),
                
                ("1", Keys.NumPad1, new Point(10, 115), new Size(40, 30)),
                ("2", Keys.NumPad2, new Point(55, 115), new Size(40, 30)),
                ("3", Keys.NumPad3, new Point(100, 115), new Size(40, 30)),
                ("Enter", Keys.Return, new Point(145, 115), new Size(40, 65)),
                
                ("0", Keys.NumPad0, new Point(10, 150), new Size(85, 30)),
                (".", Keys.Decimal, new Point(100, 150), new Size(40, 30))
            };

            foreach (var (text, key, location, size) in numpadKeys)
            {
                Button btn = CreateKeyButton(text, size, location);
                keyButtons[key] = btn;
                numpadPanel.Controls.Add(btn);
            }
        }

        private Button CreateKeyButton(string text, Size size, Point location)
        {
            return new Button
            {
                Text = text,
                Size = size,
                Location = location,
                BackColor = Color.FromArgb(240, 240, 240),
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 8, FontStyle.Bold),
                UseVisualStyleBackColor = false,
                ForeColor = Color.FromArgb(50, 50, 50),
                FlatAppearance = { BorderColor = Color.FromArgb(200, 200, 200), BorderSize = 1 }
            };
        }

        private Size GetKeySize(string keyText)
        {
            return keyText switch
            {
                "Backspace" => new Size(80, 40),
                "Tab" => new Size(60, 40),
                "Caps" => new Size(70, 40),
                "Enter" => new Size(80, 40),
                "Shift" => new Size(90, 40),
                "Ctrl" => new Size(50, 40),
                "Alt" => new Size(50, 40),
                "Win" => new Size(50, 40),
                "Menu" => new Size(50, 40),
                "Fn" => new Size(40, 40),
                "Space" => new Size(300, 40),
                _ => new Size(40, 40)
            };
        }

        private void KeyboardTesterForm_KeyDown(object sender, KeyEventArgs e)
        {
            // Suppress system key handling for function keys and other special keys
            e.SuppressKeyPress = true;
            e.Handled = true;

            // Most key handling is now done in ProcessCmdKey for better control
            // This method is kept for compatibility but ProcessCmdKey handles the main logic
        }

        private void KeyboardTesterForm_KeyUp(object sender, KeyEventArgs e)
        {
            e.Handled = true;

            // Key up handling is now managed by the timer in ProcessCmdKey
            // This method is kept for compatibility
        }

        private void ResetButton_Click(object sender, EventArgs e)
        {
            testedKeys.Clear();
            foreach (var kvp in keyButtons)
            {
                kvp.Value.BackColor = Color.FromArgb(240, 240, 240); // Reset to original gray
            }
            UpdateStatusLabel();
        }

        private void ToggleTestingModeButton_Click(object sender, EventArgs e)
        {
            _testingModeActive = !_testingModeActive;
            UpdateTestingModeDisplay();
        }

        private void UpdateTestingModeDisplay()
        {
            if (_testingModeActive)
            {
                testingModeLabel.Text = "🔒 TESTING MODE: System keys disabled";
                testingModeLabel.ForeColor = Color.FromArgb(220, 20, 60);
                testingModeLabel.BackColor = Color.FromArgb(255, 255, 224);
                toggleTestingModeButton.Text = "Disable Testing Mode";
                toggleTestingModeButton.BackColor = Color.FromArgb(220, 20, 60);
            }
            else
            {
                testingModeLabel.Text = "⚠️ NORMAL MODE: System keys enabled";
                testingModeLabel.ForeColor = Color.FromArgb(255, 140, 0);
                testingModeLabel.BackColor = Color.FromArgb(255, 248, 220);
                toggleTestingModeButton.Text = "Enable Testing Mode";
                toggleTestingModeButton.BackColor = Color.FromArgb(34, 139, 34);
            }
        }

        private void KeyPollingTimer_Tick(object sender, EventArgs e)
        {
            if (!_testingModeActive) return;

            // Poll for special keys that are hard to detect through normal events
            var specialKeys = new Dictionary<int, Keys>
            {
                { 0x5B, Keys.LWin },    // VK_LWIN
                { 0x5C, Keys.RWin },    // VK_RWIN
                { 0x5D, Keys.Apps }     // VK_APPS (Menu key)
            };

            var currentlyPressed = new HashSet<Keys>();

            foreach (var kvp in specialKeys)
            {
                if ((GetAsyncKeyState(kvp.Key) & 0x8000) != 0) // Key is currently pressed
                {
                    currentlyPressed.Add(kvp.Value);

                    // If this key wasn't pressed in the last poll and we have a button for it
                    if (!lastPolledKeys.Contains(kvp.Value) && keyButtons.ContainsKey(kvp.Value))
                    {
                        // Key was just pressed
                        testedKeys.Add(kvp.Value);
                        keyButtons[kvp.Value].BackColor = Color.Yellow;
                        UpdateStatusLabel();

                        // Set up timer to change color back to green
                        var colorTimer = new System.Windows.Forms.Timer();
                        colorTimer.Interval = 300;
                        colorTimer.Tick += (s, args) =>
                        {
                            if (keyButtons.ContainsKey(kvp.Value))
                            {
                                keyButtons[kvp.Value].BackColor = Color.LightGreen;
                            }
                            colorTimer.Stop();
                            colorTimer.Dispose();
                        };
                        colorTimer.Start();
                    }
                }
            }

            lastPolledKeys = currentlyPressed;
        }

        private void UpdateStatusLabel()
        {
            int totalKeys = keyButtons.Count;
            int testedCount = testedKeys.Count;
            double percentage = totalKeys > 0 ? (double)testedCount / totalKeys * 100 : 0;
            statusLabel.Text = $"Keys tested: {testedCount}/{totalKeys} ({percentage:F1}%) - Press any key to test it. Green = tested, Gray = untested, Yellow = currently pressed";
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            // Only suppress system actions if testing mode is active
            if (!_testingModeActive)
            {
                return base.ProcessCmdKey(ref msg, keyData);
            }

            // Extract the base key without modifiers for testing
            Keys baseKey = keyData & Keys.KeyCode;
            Keys keyToTest = baseKey;

            // Handle shift keys using Win32 API
            if (baseKey == Keys.ShiftKey)
            {
                if ((GetAsyncKeyState(0xA1) & 0x8000) != 0) // VK_RSHIFT = 0xA1
                {
                    keyToTest = Keys.RShiftKey;
                }
                else if ((GetAsyncKeyState(0xA0) & 0x8000) != 0) // VK_LSHIFT = 0xA0
                {
                    keyToTest = Keys.LShiftKey;
                }
            }

            // Handle control keys
            if (baseKey == Keys.ControlKey)
            {
                if ((GetAsyncKeyState(0xA3) & 0x8000) != 0) // VK_RCONTROL = 0xA3
                {
                    keyToTest = Keys.RControlKey;
                }
                else
                {
                    keyToTest = Keys.LControlKey;
                }
            }

            // Handle alt keys
            if (baseKey == Keys.Menu)
            {
                if ((GetAsyncKeyState(0xA5) & 0x8000) != 0) // VK_RMENU = 0xA5
                {
                    keyToTest = Keys.RMenu;
                }
                else
                {
                    keyToTest = Keys.LMenu;
                }
            }

            // FIXED: Handle Enter keys - corrected logic for main keyboard Enter
            if (baseKey == Keys.Return || baseKey == Keys.Enter)
            {
                const int WM_KEYDOWN = 0x0100;
                const int WM_SYSKEYDOWN = 0x0104;

                if (msg.Msg == WM_KEYDOWN || msg.Msg == WM_SYSKEYDOWN)
                {
                    // Extract the extended key flag from lParam (bit 24)
                    bool isExtended = ((msg.LParam.ToInt32() >> 24) & 0x01) != 0;

                    // Main keyboard Enter is NOT extended, Numpad Enter IS extended
                    // This is the opposite of what was previously implemented
                    if (!isExtended && keyButtons.ContainsKey(Keys.Enter))
                    {
                        keyToTest = Keys.Enter; // Main keyboard Enter (NOT extended)
                    }
                    else if (isExtended && keyButtons.ContainsKey(Keys.Return))
                    {
                        keyToTest = Keys.Return; // Numpad Enter (extended)
                    }
                }
            }

            // Handle backslash key specifically
            if (baseKey == Keys.OemBackslash || baseKey == Keys.Oem5)
            {
                if (keyButtons.ContainsKey(Keys.OemBackslash))
                {
                    keyToTest = Keys.OemBackslash;
                }
            }

            // Handle Windows keys
            if (baseKey == Keys.LWin || baseKey == Keys.RWin)
            {
                keyToTest = baseKey; // Use the specific Windows key
            }

            // Handle Menu key (Apps key)
            if (baseKey == Keys.Apps)
            {
                keyToTest = Keys.Apps;
            }

            // Test the key if it exists in our keyboard layout
            if (keyButtons.ContainsKey(keyToTest))
            {
                testedKeys.Add(keyToTest);
                keyButtons[keyToTest].BackColor = Color.Yellow; // Currently pressed
                UpdateStatusLabel();

                // Use a timer to change color back to green after a brief moment
                var timer = new System.Windows.Forms.Timer();
                timer.Interval = 200;
                timer.Tick += (s, args) =>
                {
                    if (keyButtons.ContainsKey(keyToTest))
                    {
                        keyButtons[keyToTest].BackColor = Color.LightGreen;
                    }
                    timer.Stop();
                    timer.Dispose();
                };
                timer.Start();
            }

            // Return true only in testing mode to prevent system processing
            return _testingModeActive;
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // Stop and dispose the polling timer
            if (keyPollingTimer != null)
            {
                keyPollingTimer.Stop();
                keyPollingTimer.Dispose();
            }

            // Unhook the low-level keyboard hook
            if (_hookID != IntPtr.Zero)
            {
                UnhookWindowsHookEx(_hookID);
            }

            // Show completion message if all keys were tested
            if (testedKeys.Count == keyButtons.Count && keyButtons.Count > 0)
            {
                MessageBox.Show($"Congratulations! You have successfully tested all {keyButtons.Count} keys on your keyboard!",
                    "Keyboard Test Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            base.OnFormClosing(e);
        }

        private static IntPtr SetHook(LowLevelKeyboardProc proc)
        {
            using (var curProcess = System.Diagnostics.Process.GetCurrentProcess())
            using (var curModule = curProcess.MainModule)
            {
                return SetWindowsHookEx(WH_KEYBOARD_LL, proc,
                    GetModuleHandle(curModule?.ModuleName), 0);
            }
        }

        private static IntPtr HookCallback(int nCode, IntPtr wParam, IntPtr lParam)
        {
            if (nCode >= 0 && _testingModeActive && _instance != null)
            {
                // Read the virtual key code from lParam
                int vkCode = System.Runtime.InteropServices.Marshal.ReadInt32(lParam);

                // Handle key down events
                if (wParam == (IntPtr)WM_KEYDOWN_HOOK || wParam == (IntPtr)WM_SYSKEYDOWN_HOOK)
                {
                    // Handle special keys that need to be processed for testing but suppressed from system
                    Keys? keyToProcess = null;

                    switch (vkCode)
                    {
                        case 0x5B: // VK_LWIN (Left Windows key)
                            keyToProcess = Keys.LWin;
                            break;
                        case 0x5C: // VK_RWIN (Right Windows key) - if it exists in layout
                            keyToProcess = Keys.RWin;
                            break;
                        case 0x5D: // VK_APPS (Menu key)
                            keyToProcess = Keys.Apps;
                            break;
                        case 0xFF: // VK_FN (Fn key on some keyboards)
                            keyToProcess = Keys.None;
                            break;
                    }

                    // Process the key for testing if it's in our layout
                    if (keyToProcess.HasValue && _instance.keyButtons.ContainsKey(keyToProcess.Value))
                    {
                        try
                        {
                            _instance.Invoke(new Action(() =>
                            {
                                _instance.testedKeys.Add(keyToProcess.Value);
                                _instance.keyButtons[keyToProcess.Value].BackColor = Color.Yellow;
                                _instance.UpdateStatusLabel();

                                var timer = new System.Windows.Forms.Timer();
                                timer.Interval = 300;
                                timer.Tick += (s, args) =>
                                {
                                    _instance.keyButtons[keyToProcess.Value].BackColor = Color.LightGreen;
                                    timer.Stop();
                                    timer.Dispose();
                                };
                                timer.Start();
                            }));
                        }
                        catch { /* Ignore invoke errors */ }
                    }

                    // List of system keys that should be completely suppressed during testing
                    var systemKeysToSuppress = new HashSet<int>
                    {
                        0x5B, // VK_LWIN (Left Windows key)
                        0x5C, // VK_RWIN (Right Windows key)
                        0x70, // VK_F1 (Function keys that might have system bindings)
                        0x71, // VK_F2
                        0x72, // VK_F3
                        0x73, // VK_F4
                        0x74, // VK_F5
                        0x75, // VK_F6
                        0x76, // VK_F7
                        0x77, // VK_F8
                        0x78, // VK_F9
                        0x79, // VK_F10
                        0x7A, // VK_F11
                        0x7B, // VK_F12
                        0x5D, // VK_APPS (Menu key)
                        0xFF  // VK_FN (Fn key on some keyboards)
                    };

                    // Suppress system keys during testing mode
                    if (systemKeysToSuppress.Contains(vkCode))
                    {
                        return (IntPtr)1; // Suppress the key
                    }
                }
            }

            return CallNextHookEx(_hookID, nCode, wParam, lParam);
        }
    }
}
